import { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useAudioPlayer, AudioModule } from 'expo-audio';
import * as FileSystem from 'expo-file-system';

interface AudioContextType {
  isPlaying: boolean;
  currentSound: {
    title: string;
    url: string;
    duration: number;
    position: number;
    storyId: number;
  } | null;
  playSound: (title: string, url: string, storyId: number) => Promise<void>;
  pauseSound: () => Promise<void>;
  resumeSound: () => Promise<void>;
  stopSound: () => Promise<void>;
  seekTo: (position: number) => Promise<void>;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

export function AudioProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const configureAudio = async () => {
      try {
        // Configure audio session for background playback
        await AudioModule.setAudioModeAsync({
          staysActiveInBackground: true,
          shouldPlayInBackground: true,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
        });
      } catch (error) {
        console.error('Error setting audio mode:', error);
      }
    };

    configureAudio();
  }, []);

  const player = useAudioPlayer();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSound, setCurrentSound] = useState<AudioContextType['currentSound']>(null);
  const positionIntervalRef = useRef<number | null>(null);
  const durationCheckIntervalRef = useRef<number | null>(null);

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      if (positionIntervalRef.current) {
        clearInterval(positionIntervalRef.current);
      }
      if (durationCheckIntervalRef.current) {
        clearInterval(durationCheckIntervalRef.current);
      }
    };
  }, []);

  const getLocalAudioPath = (url: string) => {
    const fileName = url.split('/').pop();
    return `${FileSystem.documentDirectory}audio_${fileName}`;
  };

  const startPositionTracking = () => {
    // Update position every second
    positionIntervalRef.current = setInterval(() => {
      if (player.playing) {
        setCurrentSound(prev => {
          if (!prev) return null;
          return {
            ...prev,
            position: player.currentTime,
          };
        });

        // Check if playback finished
        if (player.duration > 0 && player.currentTime >= player.duration) {
          console.log('Audio playback finished');
          stopSound();
        }
      }
    }, 1000);
  };

  const stopPositionTracking = () => {
    if (positionIntervalRef.current) {
      clearInterval(positionIntervalRef.current);
      positionIntervalRef.current = null;
    }
  };

  const startDurationCheck = () => {
    let attempts = 0;
    const maxAttempts = 50; // Try for up to 5 seconds (50 * 100ms)

    durationCheckIntervalRef.current = setInterval(() => {
      attempts++;

      if (player?.duration > 0) {
        const duration = player.duration;
        console.log('Audio duration found after', attempts, 'attempts:', duration, 'seconds');
        setCurrentSound(prev => prev ? ({
          ...prev,
          duration: duration,
        }) : null);

        // Clear the interval once duration is found
        if (durationCheckIntervalRef.current) {
          clearInterval(durationCheckIntervalRef.current);
          durationCheckIntervalRef.current = null;
        }
      } else if (attempts >= maxAttempts) {
        console.warn('Could not get audio duration after', maxAttempts, 'attempts');
        // Clear the interval after max attempts
        if (durationCheckIntervalRef.current) {
          clearInterval(durationCheckIntervalRef.current);
          durationCheckIntervalRef.current = null;
        }
      }
    }, 100); // Check every 100ms
  };

  const stopDurationCheck = () => {
    if (durationCheckIntervalRef.current) {
      clearInterval(durationCheckIntervalRef.current);
      durationCheckIntervalRef.current = null;
    }
  };

  const playSound = async (title: string, url: string, storyId: number) => {
    // Stop any currently playing sound
    if (player.playing) {
      console.log('Stopping previous sound before playing new one');
      await stopSound();
    }

    const localPath = getLocalAudioPath(url);
    let audioUri = url;
    
    try {
      const fileInfo = await FileSystem.getInfoAsync(localPath);
      if (fileInfo.exists) {
        console.log('Found cached audio file, using local version:', localPath);
        audioUri = localPath;
      } else {
        console.log('No cached version found, streaming from URL:', url);
        console.log('Starting download in background...');
        // Start download but don't await it
        FileSystem.downloadAsync(url, localPath)
          .then(() => console.log('Audio file downloaded successfully:', localPath))
          .catch(error => console.error('Download error:', error));
      }
    } catch (error) {
      console.error('Error checking file:', error);
      // Fallback to URL if there's any error
      audioUri = url;
    }

    console.log('Loading Sound from:', audioUri);
    
    // Replace the audio source
    await player.replace({ uri: audioUri });
    
    const currentSoundTmp = {
      title,
      url,
      duration: 0,
      position: 0,
      storyId,
    };
    setCurrentSound(currentSoundTmp);

    console.log('Playing Sound:', currentSoundTmp);
    player.play();
    setIsPlaying(true);

    // Start tracking position
    startPositionTracking();

    // Start periodic duration checking
    startDurationCheck();
  };

  const pauseSound = async () => {
    if (player.playing) {
      player.pause();
      setIsPlaying(false);
    }
  };

  const resumeSound = async () => {
    if (!player.playing && currentSound) {
      player.play();
      setIsPlaying(true);
    }
  };

  const stopSound = async () => {
    stopPositionTracking();
    stopDurationCheck();
    setCurrentSound(null);
    setIsPlaying(false);

    if (player.playing) {
      player.pause();
      player.seekTo(0);
    }
  };

  const seekTo = async (position: number) => {
    if (currentSound) {
      if (position > currentSound.duration) {
        position = currentSound.duration;
      } else if (position < 0) {
        position = 0;
      }

      player.seekTo(position);
      setCurrentSound(prev => prev ? {
        ...prev,
        position: position,
      } : null);
    }
  };

  return (
    <AudioContext.Provider
      value={{
        isPlaying,
        currentSound,
        playSound,
        pauseSound,
        resumeSound,
        stopSound,
        seekTo,
      }}>
      {children}
    </AudioContext.Provider>
  );
}

export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};