import { adapty } from 'react-native-adapty';
import { createPaywallView } from 'react-native-adapty/dist/ui';
import { getLocale, getTranslation } from './LocalizationUtil';
import { getUserIdCreateIfNot } from './LoginUtil';
import { Platform, NativeModules, Alert } from 'react-native';

export const getSubscriptionStatus = async () => {
    try {
        const profile = await adapty.getProfile();
        const isActive = profile?.accessLevels?.["premium"]?.isActive;
        return isActive;
    } catch (e) {
        console.error("(ADAPTY) Error on getSubscriptionStatus:", e);
        return false;
    }
}


export const adaptyIdentify = async () => {
    try {
        const userId = await getUserIdCreateIfNot();
        if (userId) {
            adapty.identify(userId);
        } else {
            throw new Error("userId is null");
        }
    } catch (e) {
        console.error("(ADAPTY) Error on adaptyIdentify:", e);
    }
}


export const displayPaywall = async (placementName: string) => {
    try {
        const locale = getLocale();
        console.log("(ADAPTY) Displaying paywall for placement:", placementName, "with locale:", locale);
        const paywall = await adapty.getPaywall(placementName, locale);
        console.log("(ADAPTY) Paywall:", paywall);
        if (paywall.hasViewConfiguration) {
            const view = await createPaywallView(paywall);
            view.registerEventHandlers();
            view.present();
        } else {
            throw new Error("Paywall has no view configuration");
        }
    } catch (e) {
        console.error("(ADAPTY) Error on displayPaywall:", e);
    }
}

// Extra Credits
export const purchaseCredit = async (successCallback: (isSuccess: boolean) => void) => {
    // TODO: Migrate to Superwall
    // This function should handle credit purchases using Superwall SDK
    console.log("Credit purchase pending Superwall migration");
    successCallback(false); // Return false for now to prevent runtime errors
};

export const getCreditPrice = async () => {
    // TODO: Migrate to Superwall
    // This function should fetch credit price from Superwall SDK
    return "$9.98"; // Return default price for now
};