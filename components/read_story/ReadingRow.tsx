import { useState } from "react";
import { ActivityIndicator, Pressable, View, Text, Image } from "react-native";
import { getTranslation } from "../util/LocalizationUtil";
import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { Ionicons } from "@expo/vector-icons";
import { VoiceRowStyles } from './VoiceRowStyles';
import LocalPersistentCacheImage from "@/components/LocalPersistentCacheImage";
import { CloneImagePlaceholder } from '../commonComponents/readStory/CloneImagePlaceholder';
import { R2_URL_SPECIAL_VOICE_IMAGES, R2_URL_STORY_READINGS } from "@/constants/Constants";
import { SpecialVoice } from "@/contexts/SpecialVoicesContext";

interface ReadingRowProps {
    reading: any;
    clones: any[];
    specialVoices: SpecialVoice[];
    getAndSetStoryReadings: () => Promise<any>;
    toggleVoicesList: () => void;
    handlePlay: (url: string) => Promise<void>;
    router: any;
    id: string | string[];
}

const ReadingRow = ({ 
    reading, 
    clones, 
    specialVoices, 
    getAndSetStoryReadings, 
    toggleVoicesList, 
    handlePlay, 
    router, 
    id 
}: ReadingRowProps) => {
    const [isLoading, setIsLoading] = useState(false);

    const startPlaying = async () => {
        console.log("startPlaying");
        setIsLoading(true);
        try {
            const storyReadings = await getAndSetStoryReadings();
            console.log("storyReadings: ", storyReadings)
            const storyReadingToPlayInMethod = storyReadings.find((x: any) => x.id === reading.id);
            if (storyReadingToPlayInMethod) {
                if (storyReadingToPlayInMethod.status === 6) {
                    toggleVoicesList();
                    await handlePlay(`${R2_URL_STORY_READINGS}/story_reading_${storyReadingToPlayInMethod.id}.mp3`);
                } else {
                    toggleVoicesList();
                    router.push(`/reading_generating?startingPercent=50&ttsId=${storyReadingToPlayInMethod.id}&storyId=${id}`)
                }
            }
        } finally {
            setIsLoading(false);
        }
    }

    return (
        <View style={VoiceRowStyles.voiceSelectionRow}>
            {(reading.type === 1 || reading.type === 2) &&
                <Image source={reading.type === 1 ? require("@/assets/images/standard_voice/standard_voice_female.jpg") : require("@/assets/images/standard_voice/standard_voice_male.jpg")} style={VoiceRowStyles.voiceSelectionImage} />
            }
            {reading.type === 3 &&
                <LocalPersistentCacheImage url={`${R2_URL_SPECIAL_VOICE_IMAGES}/special_voice_image_${reading.voice_id}.jpg`} style={VoiceRowStyles.voiceSelectionImage} />
            }
            {
                reading.type === 0 &&
                <CloneImagePlaceholder name={clones.find((clone: any) => clone.voiceId === reading.voice_id)?.name} voiceId={reading.voice_id} />
            }
            <Text style={VoiceRowStyles.voiceNameText}>
                {
                    reading.type === 0 ? clones.find((clone: any) => clone.voiceId === reading.voice_id)?.name :
                        reading.type === 1 ? getTranslation('female') :
                            reading.type === 2 ? getTranslation('male') :
                                reading.type === 3 ? specialVoices.find((voice: SpecialVoice) => voice.id === reading.voice_id)?.name :
                                    ""
                }
                {reading.status !== 6 && " (" + getTranslation('generating') + ")"}
            </Text>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={[VoiceRowStyles.createReadingButton, { flexDirection: 'row' }]} onPress={startPlaying}>
                {isLoading ? <ActivityIndicator size={17} color={Colors['light'].text} /> :
                    <>
                        <Ionicons name={"play"} size={16} color={Colors['light'].text} />
                        <Text style={VoiceRowStyles.createCloneButtonText}>{getTranslation('listen')}</Text>
                    </>
                }
            </Pressable>
        </View>
    )
}

export default ReadingRow;
