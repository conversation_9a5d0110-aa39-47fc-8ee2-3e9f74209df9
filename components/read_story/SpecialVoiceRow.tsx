import { useEffect, useState } from "react";
import { ActivityIndicator, Pressable, View, Text } from "react-native";
import { getTranslation } from "../util/LocalizationUtil";
import { Colors } from "@/constants/Colors";
import { MAIN_STYLES } from "@/constants/Styles";
import { Ionicons } from "@expo/vector-icons";
import { VoiceRowStyles } from './VoiceRowStyles';
import LocalPersistentCacheImage from "@/components/LocalPersistentCacheImage";
import { R2_URL_SPECIAL_VOICE_IMAGES, R2_URL_SPECIAL_VOICES } from "@/constants/Constants";
import { SpecialVoice } from "@/contexts/SpecialVoicesContext";
import { useAudioPlayer } from 'expo-audio';

interface SpecialVoiceRowProps {
    voice: SpecialVoice;
    showConfirmationModal: (action: {
        type: 'standard' | 'clone' | 'special';
        voiceId?: number;
        voiceType?: number;
    }) => void;
}

const SpecialVoiceRow = ({ voice, showConfirmationModal }: SpecialVoiceRowProps) => {
    const audioUrl = `${R2_URL_SPECIAL_VOICES}/special_voice_${voice.id}.mp3`;
    const [isPlaying, setIsPlaying] = useState(false);
    const [isCreatingReading, setIsCreatingReading] = useState(false);
    const audioFile = { uri: audioUrl };
    const player = useAudioPlayer(audioFile);

    useEffect(() => {
        const handlePlaybackStatusUpdate = () => {
            if (player.playing !== isPlaying) {
                setIsPlaying(player.playing);
            }
        };

        // Check playback status periodically
        const interval = setInterval(handlePlaybackStatusUpdate, 100);

        return () => {
            clearInterval(interval);
            // TODO: Redo with new player lib
            /*
            if (player.playing) {
                player.pause();
            }   
                */
        };
    }, [player.playing, isPlaying]);

    const playSound = async () => {
        try {
            if (isPlaying) return;

            setIsPlaying(true);
            player.play();
        } catch (error) {
            console.error('Error playing sound:', error);
            setIsPlaying(false);
        }
    };

    const handleCreateReading = () => {
        showConfirmationModal({
            type: 'special',
            voiceId: voice.id
        });
    }

    return (
        <View style={VoiceRowStyles.voiceSelectionRow}>
            <LocalPersistentCacheImage url={`${R2_URL_SPECIAL_VOICE_IMAGES}/special_voice_image_${voice.id}.jpg`} style={VoiceRowStyles.voiceSelectionImage} />
            <Pressable
                style={VoiceRowStyles.voicePreviewButton}
                onPress={playSound}
            >
                {isPlaying ?
                    <ActivityIndicator size="small" color={Colors['light'].text} /> :
                    <Ionicons name="play" size={16} color={Colors['light'].text} />
                }
            </Pressable>
            <View style={MAIN_STYLES.col}>
                <Text style={VoiceRowStyles.voiceNameText}>{voice.name}</Text>
                <Text style={VoiceRowStyles.creditText}>{getTranslation('tenCredits')}</Text>
            </View>
            <View style={MAIN_STYLES.flex1} />
            <Pressable style={[VoiceRowStyles.createReadingButton, isCreatingReading && MAIN_STYLES.mainButtonDisabled]} onPress={handleCreateReading} disabled={isCreatingReading}>
                {isCreatingReading ? <ActivityIndicator size={17} color={Colors['light'].text} /> :
                    <Text style={VoiceRowStyles.createCloneButtonText}>{getTranslation('createReading')}</Text>
                }
            </Pressable>
        </View>
    )
}

export default SpecialVoiceRow;
