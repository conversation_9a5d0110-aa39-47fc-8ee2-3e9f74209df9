import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useAudio } from '@/contexts/AudioContext';
import { Colors } from '@/constants/Colors';
import { R2_URL_STORY_IMAGES } from '@/constants/Constants';
import LocalPersistentCacheImage from '@/components/LocalPersistentCacheImage';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import Slider from '@react-native-community/slider';
import { MAIN_STYLES } from '@/constants/Styles';
import { getTranslationWithParams } from '@/components/util/LocalizationUtil';
import { useEffect } from 'react';
import { useRouter } from 'expo-router';

export default function Player() {
    const router = useRouter();
    const { currentSound, isPlaying, pauseSound, resumeSound, seekTo } = useAudio();

    useEffect(() => {
        if (!currentSound) {
            router.back();
        }
    }, [currentSound]);

    const handlePlayPause = async () => {
        if (isPlaying) {
            await pauseSound();
        } else {
            await resumeSound();
        }
    };

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return getTranslationWithParams('playerTimeFormat', {
            mins,
            secs: secs.toString().padStart(2, '0')
        });
    };

    const handleSeek = async (position: number) => {
        await seekTo(position);
    };

    return (
        currentSound ? (
            <View style={styles.container}>
                <LocalPersistentCacheImage
                    url={`${R2_URL_STORY_IMAGES}/image_${currentSound.storyId}.webp`}
                    style={styles.image}
                />

                <Text style={styles.title}>{currentSound.title}</Text>

                <View style={styles.sliderContainer}>
                    <Slider
                        style={styles.slider}
                        minimumValue={0}
                        maximumValue={currentSound.duration}
                        value={currentSound.position}
                        onSlidingComplete={handleSeek}
                        minimumTrackTintColor={Colors.light.tabIconSelected}
                        maximumTrackTintColor={Colors.light.tabBarBackground}
                        thumbTintColor={Colors.light.tabIconSelected}
                    />
                    <View style={styles.timeContainer}>
                        <Text style={styles.timeText}>{formatTime(currentSound.position)}</Text>
                        <Text style={styles.timeText}>{formatTime(currentSound.duration)}</Text>
                    </View>
                </View>

                <View style={styles.controls}>
                    <TouchableOpacity
                        style={styles.secondaryButton}
                        onPress={() => handleSeek(currentSound.position - 10)}>
                        <MaterialCommunityIcons name="rewind-10" size={24} color={Colors.light.text} />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.playButton}
                        onPress={handlePlayPause}>
                        <Ionicons
                            name={isPlaying ? "pause" : "play"}
                            size={32}
                            color={Colors.light.text}
                        />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.secondaryButton}
                        onPress={() => handleSeek(currentSound.position + 10)}>
                        <MaterialCommunityIcons name="fast-forward-10" size={24} color={Colors.light.text} />
                    </TouchableOpacity>
                </View>
            </View>
        ) : <View style={MAIN_STYLES.mainView} />
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.light.background,
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: 60,
        paddingHorizontal: 20,
    },
    image: {
        width: 250,
        height: 250,
        borderRadius: 20,
        marginBottom: 40,
    },
    title: {
        color: Colors.light.text,
        fontSize: 24,
        fontWeight: '600',
        marginBottom: 40,
        textAlign: 'center',
    },
    sliderContainer: {
        width: '100%',
        marginBottom: 40,
    },
    slider: {
        width: '100%',
        height: 40,
    },
    timeContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        paddingHorizontal: 15,
    },
    timeText: {
        color: Colors.light.text,
        fontSize: 14,
    },
    controls: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 20,
    },
    playButton: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: Colors.light.tabIconSelected,
        justifyContent: 'center',
        alignItems: 'center',
    },
    secondaryButton: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: Colors.light.tabBarBackground,
        justifyContent: 'center',
        alignItems: 'center',
    },
});