import LocalPersistentCacheImage from "@/components/LocalPersistentCacheImage";
import ParallaxScrollView from "@/components/ParallaxScrollView";
import { Text, View, StyleSheet, TouchableOpacity, Animated, Pressable, ActivityIndicator, SafeAreaView, Alert, Modal, ScrollView, Image } from "react-native";
import { MINI_PLAYER_HEIGHT, R2_URL_STORY_IMAGES } from "@/constants/Constants";
import { MAIN_STYLES } from "@/constants/Styles";
import { StoryItem, addFavStoryId, deleteStory, getFavStoryIds, getStoryById, getStoryReadings, removeFavStoryId, saveStoryReadings } from "@/components/util/StoryUtil";
import { useState, useEffect } from "react";
import { Colors } from "@/constants/Colors";
import Ionicons from '@expo/vector-icons/Ionicons';
import { getStoryDuration } from "@/components/util/StoryUtil";
import { useRef, useCallback } from "react";
import { useAudio } from '@/contexts/AudioContext';
import { API_URL } from "@/constants/Constants";
import { router, useFocusEffect, useLocalSearchParams, Link } from "expo-router";
import { loadRecordings } from "@/components/util/RecordingUtil";
import { getTranslation, getTranslationWithParams } from "@/components/util/LocalizationUtil";
import { SpecialVoice, useSpecialVoices } from "@/contexts/SpecialVoicesContext";
import { useAudioPlayer } from 'expo-audio';
import { ThemedText } from "@/components/ThemedText";
import Feather from '@expo/vector-icons/Feather';
import { getUserId } from "@/components/util/LoginUtil";
import BottomSheet from "@/components/BottomSheet";
import { displayPaywall, getSubscriptionStatus, getCreditPrice, purchaseCredit } from "@/components/util/AdaptyUtil";
import StandardVoiceRow from "@/components/read_story/StandardVoiceRow";
import ReadingRow from "@/components/read_story/ReadingRow";
import CloneRow from "@/components/read_story/CloneRow";
import SpecialVoiceRow from "@/components/read_story/SpecialVoiceRow";

// TODO: Image not appearing sometimes on first load problem

export default function ReadStoryScreen() {
    const player = useAudioPlayer();
    const { id, curated, imageLastUpdateTime } = useLocalSearchParams();
    const { currentSound, isPlaying, pauseSound, resumeSound, playSound } = useAudio();
    const { specialVoices } = useSpecialVoices();
    const [specialVoicesToRender, setSpecialVoicesToRender] = useState<SpecialVoice[]>([]);
    const [clones, setClones] = useState([]);
    const [cloneVoicesToRender, setCloneVoicesToRender] = useState([]);
    const [standardVoicesToRender, setStandardVoicesToRender] = useState<number[]>([]);

    const [story, setStory] = useState<StoryItem | null>(null);
    const [favStoryIds, setFavStoryIds] = useState<number[]>([]);
    const [storyReadings, setStoryReadings] = useState([]);

    const [isVoiceSelectionVisible, setIsVoiceSelectionVisible] = useState(false);
    const slideAnim = useRef(new Animated.Value(0)).current;
    const [isCloneSelectionVisible, setIsCloneSelectionVisible] = useState(false);
    const slideAnimClone = useRef(new Animated.Value(0)).current;
    const breathingAnim = useRef(new Animated.Value(0)).current;

    const [isConfirmationModalVisible, setIsConfirmationModalVisible] = useState(false);
    const [pendingVoiceAction, setPendingVoiceAction] = useState<{
        type: 'standard' | 'clone' | 'special';
        voiceId?: number;
        voiceType?: number;
    } | null>(null);
    const [creditCount, setCreditCount] = useState(0);
    const [creditPriceString, setCreditPriceString] = useState('');
    const glowValue = useState(new Animated.Value(0))[0];
    const [isLoading, setIsLoading] = useState(false);
    const [isPurchasingCredit, setIsPurchasingCredit] = useState(false);

    const loadFavStoryIds = async () => {
        const favIds = await getFavStoryIds();
        setFavStoryIds(favIds);
    }

    const loadStory = async () => {
        const story = await getStoryById(parseInt(id), curated === '1');
        if (story) {
            setStory(story);
        }
    }

    const loadClones = async () => {
        const clones = await loadRecordings();
        setClones(clones);
    }

    const getAndSetStoryReadings = async () => {
        const localReadings = await getStoryReadings(parseInt(id));
        console.log("Local Readings: ", localReadings);
        setStoryReadings(localReadings);

        const userIdentifier = await getUserId();
        const url = `${API_URL}/get_story_readings?story_id=${id}&user_identifier=${userIdentifier}`;
        console.log('url', url);
        try {
            const result = await fetch(url);
            if (result.ok) {
                const readings = await result.json();
                console.log('get_story_readings', readings);
                if (readings.status === "OK" && readings.readings) {
                    setStoryReadings(readings.readings);
                    await saveStoryReadings(parseInt(id), readings.readings);
                    return readings.readings;
                } else {
                    console.log('status not ok')
                    throw new Error("Status not OK")
                }
            } else {
                console.log("result not ok")
                throw new Error("Result not OK")
            }
        } catch (e) {
            console.log("Error: ", e)
            return localReadings;
        }
    }

    useEffect(() => {
        setStory(null);
        setFavStoryIds([]);
        setStoryReadings([]);
        setClones([]);
        setIsVoiceSelectionVisible(false);
        setIsCloneSelectionVisible(false);

        loadStory();
        loadFavStoryIds();
        getAndSetStoryReadings();
        loadClones();
    }, [id]);

    useFocusEffect(
        useCallback(() => {
            getAndSetStoryReadings();
            loadClones();
        }, [])
    );

    // Only render unused special voices
    useEffect(() => {
        const usedVoiceIds = new Set(storyReadings.filter((x: any) => x.type === 3).map((x: any) => x.voice_id));
        const availableVoices = specialVoices.filter((voice: SpecialVoice) => !usedVoiceIds.has(voice.id));
        setSpecialVoicesToRender(availableVoices);
    }, [storyReadings, specialVoices]);

    // Only render unused clones
    useEffect(() => {
        const usedVoiceIds = new Set(storyReadings.filter((x: any) => x.type === 0).map((x: any) => x.voice_id));
        const availableClones = clones.filter((clone: any) => !clone.isDeleted && !usedVoiceIds.has(clone.voiceId));
        setCloneVoicesToRender(availableClones);
    }, [storyReadings, clones]);

    // Only render unused standard voices
    useEffect(() => {
        const usedVoiceTypes = new Set(storyReadings.filter((x: any) => x.type === 1 || x.type === 2).map((x: any) => x.type));
        const availableStandardVoices = [1, 2].filter((type: number) => !usedVoiceTypes.has(type));
        console.log('Available Standard Voices: ', availableStandardVoices);
        setStandardVoicesToRender(availableStandardVoices);
    }, [storyReadings]);

    useEffect(() => {
        const breathe = () => {
            Animated.sequence([
                Animated.timing(breathingAnim, {
                    toValue: 1,
                    duration: 2000,
                    useNativeDriver: true,
                }),
                Animated.timing(breathingAnim, {
                    toValue: 0,
                    duration: 2000,
                    useNativeDriver: true,
                })
            ]).start(() => breathe());
        };

        breathe();
        return () => breathingAnim.stopAnimation();
    }, []);

    const toggleVoicesList = () => {
        if (isCloneSelectionVisible) {
            toggleCloneList();
            return;
        }
        setIsVoiceSelectionVisible(!isVoiceSelectionVisible);
        slideAnim.setValue(isVoiceSelectionVisible ? 1 : 0); // Reset the animation value
        const toValue = isVoiceSelectionVisible ? 0 : 1;
        Animated.spring(slideAnim, {
            toValue,
            useNativeDriver: true,
            tension: 40,
            friction: 8
        }).start();
    };

    const toggleCloneList = () => {
        if (isVoiceSelectionVisible) {
            toggleVoicesList();
            return;
        }
        setIsCloneSelectionVisible(!isCloneSelectionVisible);
        slideAnimClone.setValue(isCloneSelectionVisible ? 1 : 0); // Reset the animation value
        const toValue = isCloneSelectionVisible ? 0 : 1;
        Animated.spring(slideAnimClone, {
            toValue,
            useNativeDriver: true,
            tension: 40,
            friction: 8
        }).start();
    };

    const closeListAndDisplayPaywall = async () => {
        setIsConfirmationModalVisible(false);
        await displayPaywall('general');
    }

    const handlePlay = async (urlToPlay: string) => {
        if (currentSound?.url === urlToPlay) {
            if (isPlaying) {
                await pauseSound();
            } else {
                await resumeSound();
            }
        } else {
            await playSound(
                story?.title || 'Unknown Story',
                urlToPlay,
                parseInt(id)
            );
        }
    };

    const handleBookmark = async () => {
        if (favStoryIds.includes(parseInt(id))) {
            await removeFavStoryId(parseInt(id));
        } else {
            await addFavStoryId(parseInt(id));
        }
        loadFavStoryIds();
    }









    useEffect(() => {
        loadUserCredits();
        loadCreditPrice();
        startGlowAnimation();
    }, []);

    const startGlowAnimation = () => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(glowValue, {
                    toValue: 1,
                    duration: 1500,
                    useNativeDriver: true,
                }),
                Animated.timing(glowValue, {
                    toValue: 0,
                    duration: 1500,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    };

    const loadCreditPrice = async () => {
        const price = await getCreditPrice();
        setCreditPriceString(price);
    };

    const handlePurchaseCredits = async () => {
        setIsPurchasingCredit(true);
        await purchaseCredit((isSuccess: boolean) => {
            setIsPurchasingCredit(false);
            if (isSuccess) {
                setCreditCount(creditCount + 50);
            }
        });
    };

    const loadUserCredits = async () => {
        const userIdentifier = await getUserId();
        if (userIdentifier) {
            try {
                const response = await fetch(`${API_URL}/get_user_credits/${userIdentifier}`);
                const data = await response.json();
                if (data.status === "OK") {
                    setCreditCount(data.total_credits);
                }
            } catch (error) {
                console.error('Error fetching user credits:', error);
            }
        }
    };

    const showConfirmationModal = async (action: typeof pendingVoiceAction) => {
        console.log("showConfirmationModal: ", action);
        const isPro = await getSubscriptionStatus();
        if (!isPro && false) { // TODO: Remove before production
            toggleVoicesList();
            setTimeout(() => {
                displayPaywall('general');
            }, 100);
            return;
        } else {
            toggleVoicesList();
            setPendingVoiceAction(action);
            setTimeout(() => {
                setIsConfirmationModalVisible(true);
            }, 100);
        }
    };

    const handleConfirmVoiceAction = async () => {
        if (!pendingVoiceAction) {
            setIsConfirmationModalVisible(false);
            return;
        };
        setIsLoading(true);

        const isPro = await getSubscriptionStatus();
        if (!isPro && false) { // TODO: Remove before production
            await closeListAndDisplayPaywall();
            return;
        }

        switch (pendingVoiceAction.type) {
            case 'standard':
                await createStandardReading(pendingVoiceAction.voiceType!);
                break;
            case 'clone':
                await createCloneReading(pendingVoiceAction.voiceId!);
                break;
            case 'special':
                await createSpecialVoiceReading(pendingVoiceAction.voiceId!);
                break;
        }
        setIsLoading(false);
        setIsConfirmationModalVisible(false);
    };

    const createStandardReading = async (type: number) => {
        try {
            const userIdentifier = await getUserId();

            const jsonBody = {
                story_id: id,
                tts_type: type,
                user_identifier: userIdentifier
            }
            const result = await fetch(`${API_URL}/create_story_openai_tts_async`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(jsonBody)
            });
            if (result.ok) {
                const json = await result.json();
                if (json.status === "OK") {
                    router.push(`/reading_generating?startingPercent=5&ttsId=${json.tts_id}&storyId=${id}&imageLastUpdateTime=${imageLastUpdateTime || ''}`)
                } else {
                    throw new Error("Failed to create reading, json: " + JSON.stringify(json));
                }
            } else {
                throw new Error("Failed to create reading, result: " + JSON.stringify(result));
            }
        } catch (error) {
            console.error("API Error: ", error)
            Alert.alert(getTranslation("generalErrorTitle"), getTranslation("generalErrorMessage"));
        }
    }

    const createCloneReading = async (voiceId: number) => {
        try {
            const userIdentifier = await getUserId();
            const jsonBody = {
                story_id: id,
                voice_id: voiceId,
                user_identifier: userIdentifier
            }
            const result = await fetch(`${API_URL}/create_story_elevenlabs_tts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(jsonBody)
            });

            if (result.ok) {
                const json = await result.json();
                if (json.status === "OK") {
                    router.push(`/reading_generating?startingPercent=5&ttsId=${json.tts_id}&storyId=${id}&isClone=true&imageLastUpdateTime=${imageLastUpdateTime || ''}`)
                } else {
                    throw new Error("Failed to create reading, json: " + JSON.stringify(json));
                }
            } else {
                throw new Error("Failed to create reading, result: " + JSON.stringify(result));
            }
        } catch (error) {
            console.error("API Error: ", error)
            Alert.alert(getTranslation("generalErrorTitle"), getTranslation("generalErrorMessage"));
        }
    }

    const createSpecialVoiceReading = async (voiceId: number) => {
        try {
            const userIdentifier = await getUserId();
            const jsonBody = {
                story_id: id,
                voice_id: voiceId,
                user_identifier: userIdentifier
            }
            const result = await fetch(`${API_URL}/create_special_voice_tts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(jsonBody)
            });

            if (result.ok) {
                const json = await result.json();
                if (json.status === "OK") {
                    router.push(`/reading_generating?startingPercent=5&ttsId=${json.tts_id}&storyId=${id}&isSpecialVoice=true&imageLastUpdateTime=${imageLastUpdateTime || ''}`)
                } else {
                    throw new Error("Failed to create reading, json: " + JSON.stringify(json));
                }
            } else {
                throw new Error("Failed to create reading, result: " + JSON.stringify(result));
            }
        } catch (error) {
            console.error("API Error: ", error)
            Alert.alert(getTranslation("generalErrorTitle"), getTranslation("generalErrorMessage"));
        }
    }

    return (
        <SafeAreaView style={MAIN_STYLES.safeArea}>
            <ParallaxScrollView headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
                headerImage={
                    <View style={styles.headerImageContainer}>
                        <LocalPersistentCacheImage style={styles.headerImage} url={`${R2_URL_STORY_IMAGES}/image_${id}.webp${imageLastUpdateTime ? `?cache=${imageLastUpdateTime}` : ''}`} />
                        <View style={styles.headerOverlay}>
                            <Link dismissTo href="/(discover)">
                                <Feather name="chevron-left" size={32} color={Colors.light.text} />
                            </Link>
                            <View style={MAIN_STYLES.flex1} />
                            <Pressable onPress={handleBookmark} style={{ marginRight: 16 }}>
                                <Ionicons
                                    name={favStoryIds.includes(parseInt(id)) ? "heart" : "heart-outline"}
                                    size={28}
                                    color={favStoryIds.includes(parseInt(id)) ? Colors['light'].likeRed : Colors['light'].text}
                                />
                            </Pressable>
                            {curated !== '1' &&
                                <Pressable onPress={() => {
                                    Alert.alert(
                                        getTranslation('deleteStory'),
                                        getTranslation('deleteStoryConfirmation'),
                                        [
                                            {
                                                text: getTranslation('cancel'),
                                                style: 'cancel'
                                            },
                                            {
                                                text: getTranslation('delete'),
                                                style: 'destructive',
                                                onPress: async () => {
                                                    await deleteStory(parseInt(parseInt(id)));
                                                    router.replace('/(tabs)/(discover)');
                                                }
                                            }
                                        ]
                                    );
                                }}>
                                    <Ionicons name="trash" size={24} color={Colors.light.text} />
                                </Pressable>
                            }
                        </View>
                    </View>
                }>
                <View style={[MAIN_STYLES.mainView, { paddingBottom: MINI_PLAYER_HEIGHT + 16 }]}>
                    <View style={[MAIN_STYLES.row, { gap: 8, marginBottom: 8, alignItems: 'center' }]}>
                        <TouchableOpacity style={[styles.playButton, { paddingHorizontal: 24 }]}
                            onPress={toggleVoicesList}>
                            <Ionicons name="play" size={16} color={Colors['light'].text} />
                            <Text style={styles.playButtonText}>{getTranslation('listen')}</Text>
                        </TouchableOpacity>
                        <Animated.View style={[
                            styles.glowingBorder,
                            {
                                transform: [{
                                    scale: breathingAnim.interpolate({
                                        inputRange: [0, 1],
                                        outputRange: [1, 1.03]
                                    })
                                }]
                            }
                        ]}>
                            <TouchableOpacity
                                style={[
                                    styles.playButton,
                                    styles.glowingBackground,
                                    { flex: 1 }
                                ]}
                                onPress={async () => {
                                    if (clones.length > 0) {
                                        toggleCloneList();
                                    } else {
                                        const isPro = await getSubscriptionStatus();
                                        if (isPro || true) { // TODO: Remove before production
                                            router.push(`/new_clone?storyId=${id}`);
                                        } else {
                                            await displayPaywall('general');
                                        }
                                    }
                                }}
                            >
                                <Ionicons name="mic" size={16} color={Colors['light'].text} />
                                <Text style={styles.playButtonText}>{getTranslation('listenFromClone')}</Text>
                            </TouchableOpacity>
                        </Animated.View>
                    </View>
                    {story?.storyText && <Text style={styles.readingTime}>{getTranslationWithParams('readingTime', { minutes: getStoryDuration(story?.storyText) })}</Text>}
                    {/* START: VOICE SELECTION MODAL */}
                    <BottomSheet
                        isVisible={isVoiceSelectionVisible}
                        onClose={toggleVoicesList}>
                        <ScrollView
                            showsVerticalScrollIndicator={true}
                            contentContainerStyle={styles.scrollViewContent}
                        >
                            {storyReadings
                                .map((item: any, index: number) => (
                                    <ReadingRow
                                        key={`reading-${item.id}`}
                                        reading={item}
                                        clones={clones}
                                        specialVoices={specialVoices}
                                        getAndSetStoryReadings={getAndSetStoryReadings}
                                        toggleVoicesList={toggleVoicesList}
                                        handlePlay={handlePlay}
                                        router={router}
                                        id={id}
                                    />
                                ))}
                            {(cloneVoicesToRender.length > 0 || standardVoicesToRender.length > 0 || specialVoicesToRender.length > 0) &&
                                <ThemedText type="h3" style={styles.availableVoicesTitle}>{getTranslation('createNewReading')}</ThemedText>
                            }
                            {/* CLONE VOICES */}
                            {cloneVoicesToRender.map((clone) => (
                                <CloneRow key={`clone-${clone.voiceId}`} clone={clone} showConfirmationModal={showConfirmationModal} />
                            ))}
                            {/* STANDRAD VOICES */}
                            {standardVoicesToRender.map((type) => (
                                <StandardVoiceRow key={`standard-voice-${type}`} type={type} showConfirmationModal={showConfirmationModal} />
                            ))}
                            {/* SPECIAL VOICES */}
                            {specialVoicesToRender.map((voice) => (
                                <SpecialVoiceRow key={`special-voice-${voice.id}`} voice={voice} showConfirmationModal={showConfirmationModal} />
                            ))}
                        </ScrollView>
                    </BottomSheet>
                    {/* END: VOICE SELECTION MODAL */}
                    {/* Clone Selection Modal */}
                    <BottomSheet
                        isVisible={isCloneSelectionVisible}
                        onClose={toggleCloneList}
                    >
                        <ScrollView
                            showsVerticalScrollIndicator={true}
                            contentContainerStyle={styles.scrollViewContent}
                        >
                            {/* Render VoiceRows first */}
                            {clones
                                .filter(clone => storyReadings.some((reading: any) => reading.voice_id === clone.voiceId))
                                .map((clone, index) => (
                                    <ReadingRow
                                        key={`reading-in-clone-${clone.voiceId}`}
                                        reading={storyReadings.find((reading: any) => reading.voice_id === clone.voiceId)}
                                        clones={clones}
                                        specialVoices={specialVoices}
                                        getAndSetStoryReadings={getAndSetStoryReadings}
                                        toggleVoicesList={toggleVoicesList}
                                        handlePlay={handlePlay}
                                        router={router}
                                        id={id}
                                    />
                                ))}
                            {/* Then render CloneRows */}
                            {clones
                                .filter(clone => !storyReadings.some((reading: any) => reading.voice_id === clone.voiceId))
                                .length > 0 &&
                                <ThemedText type="h3" style={styles.availableVoicesTitle}>{getTranslation('createNewReading')}</ThemedText>
                            }
                            {clones
                                .filter(clone => !storyReadings.some((reading: any) => reading.voice_id === clone.voiceId))
                                .map((clone) => (
                                    <CloneRow
                                        key={`clone-${clone.voiceId}`}
                                        clone={clone}
                                        showConfirmationModal={showConfirmationModal}
                                    />
                                ))}
                            <Pressable onPress={() => {
                                toggleCloneList();
                                router.push(`/new_clone?storyId=${id}`)
                            }} style={styles.newCloneButton}>
                                <Text style={styles.playButtonText}>{getTranslation('newClone')}</Text>
                            </Pressable>
                        </ScrollView>
                    </BottomSheet>
                    <ThemedText type="h1" style={styles.storyTitle}>{story?.title}</ThemedText>
                    <Text style={styles.storyText}>{story?.storyText}</Text>
                </View>
            </ParallaxScrollView >

            {/* START: CONFIRMATION MODAL */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={isConfirmationModalVisible}
                onRequestClose={() => setIsConfirmationModalVisible(false)}>
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setIsConfirmationModalVisible(false)}>
                    <Pressable
                        style={styles.modalContent}
                        onPress={e => e.stopPropagation()}>
                        <Text style={styles.modalTitle}>{getTranslation('createNewReadingTitle')}</Text>
                        <Text style={styles.modalDescription}>
                            {getTranslationWithParams('createNewReadingDescription', {
                                credits: pendingVoiceAction?.type === 'standard' ? '1' : '10'
                            })}
                        </Text>
                        <Text style={styles.modalCredits}>
                            {getTranslationWithParams('yourVoiceCredits', { credits: creditCount })}
                        </Text>

                        {creditCount >= (pendingVoiceAction?.type === 'standard' ? 1 : 10) || true ? // TODO: Remove before production
                            <View style={styles.modalButtons}>
                                <TouchableOpacity
                                    style={[styles.button, styles.cancelButton]}
                                    onPress={() => setIsConfirmationModalVisible(false)}>
                                    <Text style={styles.buttonText}>{getTranslation('cancel')}</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[styles.button, styles.confirmButton]}
                                    onPress={handleConfirmVoiceAction}
                                    disabled={isLoading}>
                                    {isLoading ?
                                        <ActivityIndicator size={17} color={Colors['light'].text} /> :
                                        <Text style={styles.buttonText}>{getTranslation('confirm')}</Text>
                                    }
                                </TouchableOpacity>
                            </View>
                            :
                            <Animated.View style={{
                                width: '100%',
                                transform: [{
                                    scale: glowValue.interpolate({
                                        inputRange: [0, 1],
                                        outputRange: [1, 1.02]
                                    })
                                }],
                                opacity: glowValue.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [0.8, 1]
                                })
                            }}>
                                <TouchableOpacity
                                    style={[styles.getPremiumButton]}
                                    onPress={handlePurchaseCredits}>
                                    {isPurchasingCredit ? (
                                        <ActivityIndicator size="small" color={Colors['light'].text} />
                                    ) : (
                                        <>
                                            <ThemedText type="semiBold" style={MAIN_STYLES.centerText}>
                                                {getTranslation('getMoreCredits')}
                                            </ThemedText>
                                            <ThemedText type='default' style={MAIN_STYLES.centerText}>{creditPriceString}</ThemedText>
                                        </>
                                    )}
                                </TouchableOpacity>
                            </Animated.View>
                        }
                    </Pressable>
                </Pressable>
            </Modal>
            {/* END: CONFIRMATION MODAL */}
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    headerImageContainer: {
        width: '100%',
        height: 400,
        position: 'relative'
    },
    headerImage: {
        width: '100%',
        height: 400
    },
    headerOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        height: 48,
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingLeft: 0,
        alignItems: 'center',
    },
    storyTitle: {
        marginBottom: 8,
    },
    storyText: {
        fontSize: 18,
        fontWeight: '400',
        color: Colors['light'].text
    },
    playButton: {
        backgroundColor: Colors.light.tint,
        borderRadius: 16,
        paddingHorizontal: 16,
        paddingVertical: 12,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        justifyContent: 'center',
    },
    playButtonText: {
        color: Colors['light'].text,
        fontWeight: '500',
        fontSize: 16
    },
    readingTime: {
        color: Colors['light'].text,
        fontWeight: '400',
        fontSize: 14,
        marginBottom: 16
    },
    voiceSelectionContainer: {
        backgroundColor: '#21283F',
        borderRadius: 16,
        padding: 16,
        paddingTop: 4,
        marginBottom: 16
    },
    voiceSelectionRow: {
        flexDirection: 'row',
        marginBottom: 8,
        borderBottomColor: '#2D344B',
        borderBottomWidth: 1,
        alignItems: 'center',
        paddingBottom: 8
    },
    availableVoicesTitle: {
        marginTop: 4,
        marginBottom: 16,
        textAlign: 'center',
        width: '100%'
    },
    newCloneButton: {
        backgroundColor: Colors.light.tint,
        borderRadius: 16,
        paddingVertical: 12,
        alignItems: 'center',
    },
    createReadingButton: {
        backgroundColor: Colors.light.tint,
        borderRadius: 8,
        paddingVertical: 8,
        alignItems: 'center',
        paddingHorizontal: 8
    },
    createCloneButtonText: {
        color: Colors['light'].text,
        fontWeight: '500',
        fontSize: 13
    },
    createCloneButton: {
        backgroundColor: '#4870FF',
        borderRadius: 16,
        paddingVertical: 12,
        alignItems: 'center',
        paddingHorizontal: 16
    },
    modal: {
        margin: 0,
        justifyContent: 'flex-end',
    },
    modalHandle: {
        height: 4,
        backgroundColor: '#00000099',
        borderRadius: 2,
        marginBottom: 8
    },
    scrollViewContent: {
        flexGrow: 1,
        paddingBottom: 20
    },
    glowingBorder: {
        flex: 1,
        shadowColor: '#9C27B0',
        shadowOffset: {
            width: 0,
            height: 0,
        },
        shadowOpacity: 0.8,
        shadowRadius: 10,
        elevation: 5,
    },
    glowingBackground: {
        backgroundColor: '#9C27B0',  // Purple base color
        shadowColor: '#9C27B0',
        shadowOffset: {
            width: 0,
            height: 0,
        },
        shadowOpacity: 0.4,
        shadowRadius: 5,
        elevation: 5,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: Colors['light'].background,
        borderRadius: 20,
        padding: 20,
        width: '80%',
        alignItems: 'center',
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: Colors['light'].text,
        marginBottom: 16,
        textAlign: 'center',
    },
    modalDescription: {
        fontSize: 16,
        color: Colors['light'].text,
        marginBottom: 12,
        textAlign: 'center',
    },
    modalCredits: {
        fontSize: 16,
        color: Colors['light'].text,
        marginBottom: 20,
        textAlign: 'center',
    },
    modalButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        gap: 12,
    },
    button: {
        padding: 12,
        borderRadius: 12,
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    cancelButton: {
        backgroundColor: Colors['light'].gray,
    },
    confirmButton: {
        backgroundColor: Colors['light'].brightGreen,
    },
    buttonText: {
        color: Colors['light'].text,
        fontSize: 16,
        fontWeight: '600',
    },
    getPremiumButton: {
        padding: 10,
        borderRadius: 16,
        width: '100%',
        marginTop: 16,
        shadowColor: Colors['light'].brightGreen,
        shadowOffset: {
            width: 0,
            height: 0,
        },
        shadowOpacity: 0.5,
        shadowRadius: 10,
        elevation: 5,
        backgroundColor: Colors['light'].brightGreen,
    },
});