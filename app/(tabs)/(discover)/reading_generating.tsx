import { MAIN_STYLES } from "@/constants/Styles";
import { View, StyleSheet, Text } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { useState, useEffect } from "react";
import { Colors } from "@/constants/Colors";
import { API_URL, R2_URL_STORY_IMAGES, R2_URL_STORY_READINGS } from "@/constants/Constants";
import { getStoryById } from "@/components/util/StoryUtil";
import { useAudio } from "@/contexts/AudioContext";
import { getTranslation } from "@/components/util/LocalizationUtil";
import TypingText from "@/components/TypingText";
import { getUserId } from "@/components/util/LoginUtil";
import { Image } from 'expo-image';

export default function ReadingGeneratingScreen() {
    const { playSound } = useAudio();
    const { startingPercent, ttsId, storyId, isClone, imageLastUpdateTime }: {
        startingPercent: string,
        ttsId: string,
        storyId: string,
        isClone: string,
        imageLastUpdateTime?: string
    } = useLocalSearchParams();
    const [percentComplete, setPercentComplete] = useState(parseInt(startingPercent));
    const [isDone, setIsDone] = useState(false);

    const getProgressMessage = () => {
        const generatingTexts = Array.from({ length: 4 }, (_, i) => 
            getTranslation(`${isClone === 'true' ? 'cloneReadingGenerating' : 'readingGenerating'}${i + 1}`)
        );
        
        // Calculate which text to show based on current progress
        const segmentSize = 100 / generatingTexts.length;
        const index = Math.min(
            Math.floor(percentComplete / segmentSize),
            generatingTexts.length - 1
        );
        
        return generatingTexts[index];
    };

    const getStoryReadings = async () => {
        const userIdentifier = await getUserId();
        const result = await fetch(`${API_URL}/get_story_readings?story_id=${storyId}&user_identifier=${userIdentifier}`);
        if (result.ok) {
            const readings = await result.json();
            if (readings.status === "OK" && readings.readings) {
                const allReadings = readings.readings;
                console.log(allReadings, parseInt(ttsId));
                const currentReading = allReadings.find((reading: any) => reading.id === parseInt(ttsId));
                console.log(currentReading);
                if (currentReading && currentReading.status === 6) {
                    // Update Percent Complete
                    setPercentComplete(100);
                    setIsDone(true);
                    // Get Story
                    let story = await getStoryById(parseInt(storyId));
                    if (story === null) {
                        story = await getStoryById(parseInt(storyId), true);
                    }
                    await playSound(
                        story?.title || 'Unknown Story',
                        `${R2_URL_STORY_READINGS}/story_reading_${ttsId}.mp3`,
                        parseInt(storyId)
                    );
                    router.back();
                }
            }
        }
    }

    useEffect(() => {
        if (!isDone) {
            const interval = setInterval(() => {
                getStoryReadings();
            }, 5000);

            return () => clearInterval(interval);
        }
    }, [storyId, isDone]);

    useEffect(() => {
        if (percentComplete < 95) {
            const interval = setInterval(() => {
                const randomIncrement = Math.random() * 2 + 0.5; // Random increment between 0.5 and 2.5
                setPercentComplete(prev => Math.min(prev + randomIncrement, 95));
            }, 1000);

            return () => clearInterval(interval);
        }
    }, [percentComplete]);

    return (
        <View style={styles.mainView}>
            <Image
                source={{ uri: `${R2_URL_STORY_IMAGES}/image_${storyId}.webp${imageLastUpdateTime ? `?cache=${imageLastUpdateTime}` : ''}` }}
                style={styles.backgroundImage}
                contentFit="cover"
                blurRadius={10}
            />
            <View style={styles.overlay} />
            <View style={styles.contentContainer}>
                <Text style={styles.titleText}>
                    {getTranslation(isClone === 'true' ? 'storyReadingGeneratingClone' : 'storyReadingGenerating')}
                </Text>
                <Text style={styles.percentText}>{Math.round(percentComplete)}%</Text>
                <TypingText text={getProgressMessage()} />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    mainView: {
        ...MAIN_STYLES.mainView,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
    backgroundImage: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
    },
    contentContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1
    },
    familyImage: {
        width: 240,
        height: 240,
        marginBottom: 16
    },
    titleText: {
        fontSize: 32,
        fontWeight: '400',
        marginBottom: 8,
        color: Colors['light'].text,
        textAlign: 'center'
    },
    percentText: {
        fontSize: 32,
        fontWeight: '400',
        color: Colors['light'].text,
        textAlign: 'center',
        marginBottom: 32
    },
})