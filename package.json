{"name": "storyaiexpo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-native-firebase/analytics": "^21.5.0", "@react-native-firebase/app": "^21.5.0", "@react-native-firebase/crashlytics": "^21.5.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/native": "^7.1.6", "expo": "^53.0.18", "expo-audio": "~0.4.8", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-system-ui": "~5.0.10", "expo-tracking-transparency": "~5.2.4", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-adapty": "^3.8.2", "react-native-device-info": "^14.0.0", "react-native-fbsdk-next": "^13.1.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-super-grid": "^6.0.1", "react-native-web": "^0.20.0", "uuid": "^11.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}